# Pytest Usage Guide for wheel-size-services
Last Modified: 2025-01-28 15:30 UTC+6

This guide covers how to use pytest for testing in the wheel-size-services Docker environment.

## Quick Start

### Running Tests

```bash
# Run a specific test file
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py

# Run all tests in a directory
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/

# Run with verbose output (already included in our script)
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py
```

## Test Structure

### Example Test File Structure

```python
"""
Unit tests for Finder-v2 Region Filtering
"""

from django.test import TestCase
from django.http import HttpRequest
from rest_framework.request import Request

from src.apps.widgets.api_proxy.views import FinderV2WidgetProxyView


class RegionParameterNormalisationTest(TestCase):
    """Test region parameter normalization in API proxy."""
    
    def test_region_array_param_is_normalised_and_overrides_widget_config(self):
        """`region[]` should become `region` and override widget config regions."""
        # Test implementation here
        pass
```

### Key Testing Patterns

#### 1. Django TestCase for Database Tests
```python
from django.test import TestCase
from django.contrib.auth.models import User
from src.apps.widgets.common.models.config import WidgetConfig

class WidgetConfigTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser_unique',  # Use unique names
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_widget_creation(self):
        config = WidgetConfig.objects.create(
            user=self.user,
            name='Test Widget',
            type='finder-v2',
            raw_params={'test': 'data'}
        )
        self.assertEqual(config.type, 'finder-v2')
```

#### 2. SimpleTestCase for Non-Database Tests
```python
from django.test import SimpleTestCase
from django.http import HttpRequest

class ParameterNormalizationTest(SimpleTestCase):
    """Use SimpleTestCase for tests that don't need database."""
    
    def test_parameter_normalization(self):
        request = HttpRequest()
        request.GET = {'region[]': ['usdm', 'cdm']}
        # Test logic here
```

#### 3. API Testing Pattern
```python
from rest_framework.test import APITestCase
from rest_framework.request import Request
from django.http import HttpRequest

class APIProxyTest(APITestCase):
    def test_api_proxy_parameter_handling(self):
        # Create mock request
        http_request = HttpRequest()
        http_request.GET = {'region[]': ['usdm', 'cdm']}
        drf_request = Request(http_request)
        
        # Test API proxy logic
        view = FinderV2WidgetProxyView()
        params = view.get_request_params(drf_request, {})
        
        self.assertIn('region', params)
        self.assertEqual(params['region'], ['usdm', 'cdm'])
```

## Test Settings

### Current Test Configuration

Our tests use `src.settings.dev_docker` which provides:
- ✅ Full Django environment compatibility
- ✅ Access to all installed apps and models
- ✅ Database access for integration tests
- ✅ Working with custom WS packages

### Test Settings File

Location: `src/settings/test.py`
- Inherits from `dev_docker.py` for compatibility
- Optimized for faster test execution
- Can be used for future pytest integration

## Running Specific Tests

### Test File Examples

```bash
# Region filtering tests (our main example)
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py

# Widget type tests (needs database cleanup)
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_widget_type.py

# All finder-v2 tests
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/
```

### Expected Output

```
🧪 Running tests using dev_docker settings...
📍 Running: tests/widget/finder_v2/test_region_filtering.py
📍 Settings: src.settings.dev_docker
📍 Python path: /code:/code/src

🔍 Running RegionParameterNormalisationTest...
test_http_request_path ... ok
test_region_array_param_is_normalised_and_overrides_widget_config ... ok

----------------------------------------------------------------------
Ran 2 tests in 0.001s

OK

============================================================
📊 SUMMARY
Tests run: 2
Failures: 0
Errors: 0
Success: True
============================================================
```

## Writing New Tests

### 1. Create Test File

```bash
# Create new test file
touch tests/widget/finder_v2/test_new_feature.py
```

### 2. Basic Test Template

```python
"""
Unit tests for New Feature
"""

from django.test import SimpleTestCase  # or TestCase for database tests
from src.apps.widgets.finder_v2.some_module import SomeClass


class NewFeatureTest(SimpleTestCase):
    """Test new feature functionality."""
    
    def setUp(self):
        """Set up test data."""
        # Initialize test data here
        pass
    
    def test_basic_functionality(self):
        """Test basic functionality works."""
        # Arrange
        test_input = "test_data"
        
        # Act
        result = SomeClass.process(test_input)
        
        # Assert
        self.assertEqual(result, "expected_output")
    
    def test_edge_case(self):
        """Test edge case handling."""
        # Test edge cases
        pass
```

### 3. Run Your New Test

```bash
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_new_feature.py
```

## Best Practices

### 1. Test Naming
- Use descriptive test method names: `test_region_array_param_is_normalised`
- Use descriptive test class names: `RegionParameterNormalisationTest`

### 2. Test Organization
- Group related tests in the same class
- Use `setUp()` for common test data
- Use `SimpleTestCase` when database not needed

### 3. Assertions
```python
# Use specific assertions
self.assertEqual(actual, expected)
self.assertIn(item, container)
self.assertTrue(condition)
self.assertIsNone(value)
self.assertRaises(Exception, callable)
```

### 4. Database Tests
```python
# For database tests, use unique identifiers
def setUp(self):
    import uuid
    unique_id = str(uuid.uuid4())[:8]
    self.user = User.objects.create_user(
        username=f'testuser_{unique_id}',
        email=f'test_{unique_id}@example.com',
        password='testpass123'
    )
```

## Common Test Patterns

### 1. API Parameter Testing
```python
def test_api_parameter_normalization(self):
    """Test API parameter normalization."""
    # Create request with array parameters
    request = HttpRequest()
    request.GET = {'region[]': ['usdm', 'cdm'], 'make[]': ['bmw']}
    
    # Test normalization
    view = FinderV2WidgetProxyView()
    params = view.get_request_params(Request(request), {})
    
    # Verify normalization
    self.assertIn('region', params)
    self.assertNotIn('region[]', params)
    self.assertEqual(params['region'], ['usdm', 'cdm'])
```

### 2. Widget Configuration Testing
```python
def test_widget_config_override(self):
    """Test widget configuration override behavior."""
    widget_config = {'regions': ['eudm']}
    request_params = {'region': ['usdm', 'cdm']}
    
    # Test that request params override widget config
    final_params = merge_params(widget_config, request_params)
    
    self.assertEqual(final_params['region'], ['usdm', 'cdm'])
```

### 3. Form Validation Testing
```python
def test_form_validation(self):
    """Test form validation logic."""
    form_data = {
        'flow_type': 'primary',
        'regions': ['usdm', 'cdm']
    }
    
    form = FinderV2ConfigForm(data=form_data)
    self.assertTrue(form.is_valid())
```

## Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Error: ModuleNotFoundError: No module named 'src.apps...'
# Solution: Check import paths match actual file structure
from src.apps.widgets.common.models.config import WidgetConfig  # Correct
from src.apps.widgets.main.models import WidgetConfig  # Incorrect
```

#### 2. Database Errors
```bash
# Error: IntegrityError: duplicate key value violates unique constraint
# Solution: Use unique identifiers in test data
username=f'testuser_{uuid.uuid4().hex[:8]}'
```

#### 3. Settings Issues
```bash
# Error: Django settings not configured
# Solution: Our test runner automatically sets DJANGO_SETTINGS_MODULE
# No manual configuration needed
```

### Debug Tips

1. **Add print statements** for debugging:
```python
def test_debug_example(self):
    result = some_function()
    print(f"Debug: result = {result}")  # Will show in test output
    self.assertEqual(result, expected)
```

2. **Use verbose output** (already enabled in our runner):
```bash
# Our test runner includes -v flag automatically
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py
```

3. **Check test file structure**:
```bash
# List test files
docker compose exec web find /code/tests -name "test_*.py" -type f
```

## Integration with Development

### 1. Before Committing Code
```bash
# Run relevant tests
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/
```

### 2. After Bug Fixes
```bash
# Run specific regression tests
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/finder_v2/test_region_filtering.py
```

### 3. Before Deployment
```bash
# Run all widget tests
docker compose exec web bash /code/scripts/run_tests.sh tests/widget/
```

## Advanced Usage

### 1. Test Markers (Future Enhancement)
```python
# Mark tests with categories
import pytest

@pytest.mark.region_filtering
def test_region_filtering_logic(self):
    pass

@pytest.mark.slow
def test_expensive_operation(self):
    pass
```

### 2. Test Fixtures (Future Enhancement)
```python
# Create reusable test data
@pytest.fixture
def sample_widget_config():
    return {
        'flow_type': 'primary',
        'regions': ['usdm', 'cdm']
    }
```

### 3. Parameterized Tests (Future Enhancement)
```python
# Test multiple scenarios
@pytest.mark.parametrize("input,expected", [
    (['usdm'], ['usdm']),
    (['usdm', 'cdm'], ['usdm', 'cdm']),
    (['eudm'], ['eudm']),
])
def test_region_normalization(self, input, expected):
    result = normalize_regions(input)
    assert result == expected
```

## Files and Directories

### Test Infrastructure
- `scripts/run_tests.sh` - Main test runner
- `src/settings/test.py` - Test-specific Django settings
- `pytest.ini` - Pytest configuration
- `docs/development/pytest-docker-integration-plan.md` - Future enhancements

### Test Locations
- `tests/widget/finder_v2/` - Finder-v2 specific tests
- `tests/widget/` - General widget tests
- `tests/` - Root test directory

### Example Test Files
- `tests/widget/finder_v2/test_region_filtering.py` - ✅ Working example
- `tests/widget/finder_v2/test_widget_type.py` - Database test example

## Summary

The pytest setup is **ready for daily use**! Key points:

✅ **Working Commands**: Use `docker compose exec web bash /code/scripts/run_tests.sh <test_file>`
✅ **Test Structure**: Follow Django TestCase patterns
✅ **Best Practices**: Use descriptive names, proper assertions, unique test data
✅ **Integration**: Run tests before commits and deployments

This setup provides a solid foundation for test-driven development in the wheel-size-services project. 